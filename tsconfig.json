{"compileOnSave": false, "typescript": "4.7.2", "compilerOptions": {"sourceMap": true, "declaration": true, "module": "commonjs", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "ES6", "typeRoots": ["node_modules/@types"], "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": "./", "resolveJsonModule": true, "esModuleInterop": true, "noUnusedLocals": true, "noUnusedParameters": true, "types": ["node"], "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "projects/**/*.ts", "global.d.ts"], "exclude": ["node_modules", "tmp"], "ts-node": {"require": ["tsconfig-paths/register"]}}